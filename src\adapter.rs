use std::{mem, slice};

use crate::{logger::*, VelociTunError, VelociTunResult};
use uuid::Uuid;
use windows::{
    core::{GUID, PCWSTR},
    Win32::{
        Devices::DeviceAndDriverInstallation::{
            SetupDiBuildDriverInfoList, SetupDiCallClassInstaller, SetupDiClassNameFromGuidW,
            SetupDiCreateDeviceInfoListExW, SetupDiCreateDeviceInfoW, SetupDiDestroyDeviceInfoList,
            SetupDiEnumDriverInfoW, SetupDiSetDeviceRegistryPropertyW, SetupDiSetSelectedDevice,
            SetupDiSetSelectedDriverW, DICD_GENERATE_ID, DIF_INSTALLDEVICE, DIF_INSTALLINTERFACES,
            DIF_REGISTERDEVICE, DIF_REGISTER_COINSTALLERS, GUID_DEVCLASS_NET, MAX_CLASS_NAME_LEN,
            SPDIT_COMPATDRIVER, SPDRP_HARDWAREID, SP_DEVINFO_DATA, SP_DRVINFO_DATA_V2_W,
        },
        NetworkManagement::{
            IpHelper::{
                ConvertInterfaceLuidToGuid, GetAdaptersAddresses, GET_ADAPTERS_ADDRESSES_FLAGS,
                IP_ADAPTER_ADDRESSES_LH,
            },
            Ndis::NET_LUID_LH,
        },
        Networking::WinSock::AF_UNSPEC,
    },
};

const HARDWARE_ID: &str = "velocitun";

/// VelociTun network adapter representation
pub struct Adapter {
    pub(crate) luid: NET_LUID_LH,
    pub(crate) name: String,
    pub(crate) tunnel_type: String,
    pub(crate) guid: GUID,
}

/// Builder for creating VelociTun adapters
#[derive(Debug)]
pub struct AdapterBuilder {
    name: Option<String>,
    tunnel_type: String,
    guid: Option<GUID>,
}

impl AdapterBuilder {
    /// Create a new adapter builder
    pub fn new() -> Self {
        Self {
            name: None,
            tunnel_type: "VelociTun".to_string(),
            guid: None,
        }
    }

    /// Set the adapter name
    pub fn name<S: Into<String>>(mut self, name: S) -> Self {
        self.name = Some(name.into());
        self
    }

    /// Set the tunnel type (default: "VelociTun")
    pub fn tunnel_type<S: Into<String>>(mut self, tunnel_type: S) -> Self {
        self.tunnel_type = tunnel_type.into();
        self
    }

    /// Set a specific GUID (optional, will generate random if not set)
    pub fn guid(mut self, guid: GUID) -> Self {
        self.guid = Some(guid);
        self
    }

    /// Build the adapter
    pub fn build(self) -> VelociTunResult<Adapter> {
        let name = self
            .name
            .unwrap_or_else(|| format!("VelociTun-{}", Uuid::new_v4().simple()));
        let guid = self.guid.unwrap_or_else(Self::generate_guid);

        log_info!("Creating adapter '{}' with GUID {:?}", name, guid);

        // Ensure driver is available
        crate::driver::Driver::ensure_available()?;

        log_info!("Creating network adapter...");

        // Create the network adapter
        let luid = Self::create_network_adapter(&name, &self.tunnel_type, &guid)?;

        Ok(Adapter {
            luid,
            name,
            tunnel_type: self.tunnel_type,
            guid,
        })
    }

    fn generate_guid() -> GUID {
        GUID::new().unwrap()
    }

    fn create_network_adapter(
        name: &str,
        _tunnel_type: &str,
        guid: &GUID,
    ) -> VelociTunResult<NET_LUID_LH> {
        unsafe {
            let mut device_name = vec![0; MAX_CLASS_NAME_LEN as usize];
            let mut required_size = 0;

            SetupDiClassNameFromGuidW(
                &GUID_DEVCLASS_NET,
                &mut device_name,
                Some(&mut required_size),
            )?;

            let device_info_set =
                match SetupDiCreateDeviceInfoListExW(Some(&GUID_DEVCLASS_NET), None, None, None) {
                    Ok(device_info_set) => device_info_set,
                    Err(err) => {
                        log_error!("Failed to create device info set: {}", err);
                        return Err(VelociTunError::WindowsApi(err));
                    }
                };

            let mut device_info_data = SP_DEVINFO_DATA {
                cbSize: std::mem::size_of::<SP_DEVINFO_DATA>() as u32,
                ClassGuid: GUID_DEVCLASS_NET,
                DevInst: 0,
                Reserved: 0,
            };

            SetupDiCreateDeviceInfoW(
                device_info_set,
                PCWSTR(device_name[..required_size as usize].as_ptr()),
                &GUID_DEVCLASS_NET,
                None,
                None,
                DICD_GENERATE_ID,
                Some(&mut device_info_data),
            )?;

            SetupDiSetSelectedDevice(device_info_set, &device_info_data as *const _ as _)?;

            SetupDiSetDeviceRegistryPropertyW(
                device_info_set,
                &mut device_info_data,
                SPDRP_HARDWAREID,
                Some(HARDWARE_ID.as_bytes()),
            )?;

            SetupDiBuildDriverInfoList(
                device_info_set,
                Some(&mut device_info_data),
                SPDIT_COMPATDRIVER,
            )?;

            log_info!("Selecting driver...");

            // Select the first available driver
            let mut driver_info: SP_DRVINFO_DATA_V2_W = mem::zeroed();
            driver_info.cbSize = mem::size_of_val(&driver_info) as _;

            SetupDiEnumDriverInfoW(
                device_info_set,
                Some(&device_info_data),
                SPDIT_COMPATDRIVER,
                0,
                &mut driver_info,
            )?;

            log_info!("Selected driver: {:?}", driver_info);

            SetupDiSetSelectedDriverW(
                device_info_set,
                Some(&mut device_info_data),
                Some(&mut driver_info),
            )?;

            log_info!("Registering device...");

            SetupDiCallClassInstaller(
                DIF_REGISTERDEVICE,
                device_info_set,
                Some(&device_info_data),
            )?;

            log_info!("Registering co-installers...");

            SetupDiCallClassInstaller(
                DIF_REGISTER_COINSTALLERS,
                device_info_set,
                Some(&device_info_data),
            )?;

            log_info!("Installing interfaces...");

            SetupDiCallClassInstaller(
                DIF_INSTALLINTERFACES,
                device_info_set,
                Some(&device_info_data),
            )?;

            log_info!("Installing device...");

            SetupDiCallClassInstaller(DIF_INSTALLDEVICE, device_info_set, Some(&device_info_data))?;

            log_info!("Device created successfully");

            let _ = SetupDiDestroyDeviceInfoList(device_info_set);

            // Wait for device and get LUID
            std::thread::sleep(std::time::Duration::from_millis(1000));
            Self::find_interface_luid_by_device(name, guid)
        }
    }

    fn find_interface_luid_by_device(
        _name: &str,
        _target_guid: &GUID,
    ) -> VelociTunResult<NET_LUID_LH> {
        use windows::Win32::NetworkManagement::IpHelper::*;

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            let mut buffer = vec![0u8; size as usize];
            let adapters = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapters),
                &mut size,
            );
            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut current = adapters;
            while !current.is_null() {
                let adapter = &*current;

                if !adapter.Description.is_null() {
                    let description = std::slice::from_raw_parts(
                        adapter.Description.0,
                        Self::wcslen_safe(adapter.Description.0),
                    );
                    let description_str = String::from_utf16_lossy(description);
                    log_info!("Found adapter: {}", description_str);
                    if description_str.contains("VelociTun") {
                        return Ok(adapter.Luid);
                    }
                }

                current = adapter.Next;
            }

            std::thread::sleep(std::time::Duration::from_secs(20));
            Err(VelociTunError::AdapterNotFound)
        }
    }

    unsafe fn wcslen_safe(s: *const u16) -> usize {
        if s.is_null() {
            return 0;
        }
        let mut len = 0;
        let mut ptr = s;
        while *ptr != 0 && len < 65536 {
            len += 1;
            ptr = ptr.add(1);
        }
        len
    }
}

impl Default for AdapterBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl Adapter {
    /// Get adapter name
    pub fn name(&self) -> &str {
        &self.name
    }

    /// Get tunnel type
    pub fn tunnel_type(&self) -> &str {
        &self.tunnel_type
    }

    /// Get adapter GUID
    pub fn guid(&self) -> &GUID {
        &self.guid
    }

    /// Get adapter LUID
    pub fn luid(&self) -> NET_LUID_LH {
        self.luid
    }

    /// Find an existing adapter by name
    pub fn find_by_name<S: AsRef<str>>(name: S) -> VelociTunResult<Self> {
        use windows::Win32::NetworkManagement::IpHelper::*;

        let name = name.as_ref();
        log_info!("Searching for adapter '{}'", name);

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            let mut buffer = vec![0u8; size as usize];
            let adapters = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapters),
                &mut size,
            );
            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut current = adapters;
            while !current.is_null() {
                let adapter = &*current;

                if !adapter.FriendlyName.is_null() {
                    let friendly_name = std::slice::from_raw_parts(
                        adapter.FriendlyName.0,
                        AdapterBuilder::wcslen_safe(adapter.FriendlyName.0),
                    );
                    let friendly_name_str = String::from_utf16_lossy(friendly_name);

                    if friendly_name_str == name {
                        let mut guid = GUID::default();
                        let result = ConvertInterfaceLuidToGuid(&adapter.Luid, &mut guid);
                        if result.is_err() {
                            return Err(VelociTunError::WindowsApi(
                                windows::core::Error::from_win32(),
                            ));
                        }

                        return Ok(Adapter {
                            luid: adapter.Luid,
                            name: name.to_string(),
                            tunnel_type: "VelociTun".to_string(),
                            guid,
                        });
                    }
                }

                current = adapter.Next;
            }

            Err(VelociTunError::AdapterNotFound)
        }
    }

    /// List all VelociTun adapters
    pub fn list_all() -> VelociTunResult<Vec<Self>> {
        let mut adapters = Vec::new();

        unsafe {
            let mut size = 0;
            GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                None,
                &mut size,
            );

            let mut buffer = vec![0u8; size as usize];
            let adapter_addrs = buffer.as_mut_ptr() as *mut IP_ADAPTER_ADDRESSES_LH;

            let result = GetAdaptersAddresses(
                AF_UNSPEC.0 as u32,
                GET_ADAPTERS_ADDRESSES_FLAGS(0),
                None,
                Some(adapter_addrs),
                &mut size,
            );
            if result != 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            let mut current = adapter_addrs;
            while !current.is_null() {
                let adapter = &*current;

                if !adapter.Description.is_null() {
                    let description = std::slice::from_raw_parts(
                        adapter.Description.0,
                        AdapterBuilder::wcslen_safe(adapter.Description.0),
                    );
                    let description_str = String::from_utf16_lossy(description);

                    if description_str.contains("VelociTun") {
                        let name = if !adapter.FriendlyName.is_null() {
                            let friendly_name = std::slice::from_raw_parts(
                                adapter.FriendlyName.0,
                                AdapterBuilder::wcslen_safe(adapter.FriendlyName.0),
                            );
                            String::from_utf16_lossy(friendly_name)
                        } else {
                            "Unknown".to_string()
                        };

                        let mut guid = GUID::default();
                        let _ = ConvertInterfaceLuidToGuid(&adapter.Luid, &mut guid);

                        adapters.push(Adapter {
                            luid: adapter.Luid,
                            name,
                            tunnel_type: "VelociTun".to_string(),
                            guid,
                        });
                    }
                }

                current = adapter.Next;
            }
        }

        Ok(adapters)
    }

    /// Create a new session for this adapter
    pub fn start_session(&self, capacity: u32) -> VelociTunResult<crate::session::Session> {
        crate::session::Session::new(std::sync::Arc::new(self.clone()), capacity)
    }
}

impl Drop for Adapter {
    fn drop(&mut self) {
        log_info!("Adapter '{}' being dropped", self.name);
    }
}

// Implement Clone for Adapter to enable sharing across sessions
impl Clone for Adapter {
    fn clone(&self) -> Self {
        Self {
            luid: self.luid,
            name: self.name.clone(),
            tunnel_type: self.tunnel_type.clone(),
            guid: self.guid,
        }
    }
}
